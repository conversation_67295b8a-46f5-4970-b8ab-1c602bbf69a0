import type { IntlFormatFormatOptions } from 'date-fns/intlFormat';

export const SCREENS = {
  sm: 480,
  mobile: 600,
  md: 768,
  lg: 1024,
  xl: 1440,
};
export const USER_KEY = 'userProfile';
export const ACCOUNT_KEY = 'userAccount';
export const TOKEN_KEY = 'access_token';

export const DATE_FORMAT_GENERIC = 'dd-MM-yyyy';

export const API_URL = process.env.NEXT_PUBLIC_HOST_URL ?? '';

export const DATE_FORMAT_CUSTOM: IntlFormatFormatOptions = {
  weekday: 'long',
  year: 'numeric',
  month: 'long',
  day: 'numeric',
};

export const TIME_FORMAT_CUSTOM: IntlFormatFormatOptions = {
  hour12: true,
  minute: 'numeric',
  hour: 'numeric',
};
export const WITHOUT_GUEST_SELECT_ID = '-1';

export enum MENU_BUTTONS {
  MORE = 'more',
  SEARCH = 'search',
  FAVORITE = 'favorite',
  RESERVATION = 'reservation',
  INBOX = 'inbox',
}

export const ROUTES_WITH_PARAMS = {
  ROOM_DETAILS: (id: number) => `/room/${id}/details`,
};

export const PROTECTED_ROUTES = ['/account', '/wishList', '/reservation', '/chat', '/notification', '/verify', '/host'];
export enum ROUTES {
  MORE = '/more',
  HOME = '/',
  LOGIN = '/login',
  FREQUENTLY_QUESTIONS = '/',
  CONTACT_US = '/contact',
  PRIVACY_POLICY = '/policy',
  USE_TERMS = '/terms-use',
  WISHLIST = '/wishList',
  RESERVATION = '/reservation',
  MY_RESERVATIONS = '/reservation?type=now',
  INBOX = '/chat',
  REGISTER = '/register',
  VERIFY_MOBILE = '/verify-mobile',
  BECOME_HOST = '/becomeHost',
  ABOUT_US = '/about',
  MY_ACCOUNT = '/account',
  HOST_PROTECTION = '/protectionHost',
  TERMS_CONDITIONS = '/terms-conditions',
  VERIFY = '/verify',
}
