import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import {
  useEditGuestAccountTypeDetails,
  useEditGuestTaxAccountTypeDetails,
  useGetAccountDetails,
  useGetVerificationCountries,
} from '@/queries/host-pages/my-account';
import AccountTypeSection from './account-type-section';
import TaxSection from './tax-section';
import SkeletonLoader from './skeleton-loader';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';
import { keys } from '@/lib/react-query/keys';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAccountTypeSchema } from './validation';
import { parseDateToString } from '@/lib/utils';
import { formatDate } from 'date-fns';

interface GuestCommercialRegistrationPayload {
  account_type: string;
  commercial_name?: string;
  commercial_registration_number?: string;
  expiry_date?: string;
  country_issue?: string;
  commercial_image?: File | undefined;
}

interface TaxVerificationPayload {
  tax_number?: string;
  tax_address?: string;
  tax_image?: File | undefined;
  registered_tax: string | number;
}

const AccountType = () => {
  const t = useScopedI18n('hostPages.accountPage');
  const [successMessage, setSuccessMessage] = useState({
    withTitle: false,
    message: '',
  });
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);

  const { isPending: accountPending, data } = useGetAccountDetails();

  const { mutateAsync: accountEditMutateAsync, isPending: editAccountPending } = useEditGuestAccountTypeDetails();
  const { data: listOfCountries } = useGetVerificationCountries();
  const { mutateAsync: editTaxMutateAsync, isPending: editTaxPending } = useEditGuestTaxAccountTypeDetails();

  const queryClient = useQueryClient();
  const accountTypeSchema = useAccountTypeSchema();

  const methods = useForm({
    defaultValues: {
      guest_commercial_registration: {
        account_type: '',
        commercial_name: '',
        commercial_registration_number: '',
        expiry_date: '',
        country_issue: '',
        commercial_image: '',
      },
      tax_verification: {
        tax_number: '',
        tax_address: '',
        tax_image: '',
        registered_tax: '0',
      },
    },
    mode: 'onChange',
    resolver: zodResolver(accountTypeSchema),
  });
  const {
    handleSubmit,
    reset,
    formState: { isDirty, dirtyFields },
  } = methods;

  const onSaveDataChanges = async (data) => {
    // Flatten guest_commercial_registration fields
    const guestCommercialRegistrationPayload: GuestCommercialRegistrationPayload =
      data.guest_commercial_registration.account_type === 'Individual'
        ? {
            account_type: data.guest_commercial_registration.account_type,
          }
        : {
            account_type: data.guest_commercial_registration.account_type,
            commercial_name: data.guest_commercial_registration.commercial_name,
            commercial_registration_number: data.guest_commercial_registration.commercial_registration_number,
            country_issue: data.guest_commercial_registration.country_issue,

            expiry_date: data.guest_commercial_registration.expiry_date
              ? formatDate(data.guest_commercial_registration.expiry_date, 'yyyy-MM-dd')
              : undefined,

            ...(typeof data.guest_commercial_registration?.commercial_image !== 'string' && {
              commercial_image: data.guest_commercial_registration?.commercial_image?.[0],
            }),
          };

    const taxVerificationPayload: TaxVerificationPayload =
      data.tax_verification.registered_tax === '0'
        ? { registered_tax: '0' }
        : {
            tax_number: data.tax_verification.tax_number,
            tax_address: data.tax_verification.tax_address,
            registered_tax: Number(data.tax_verification.registered_tax),
            ...(typeof data.tax_verification?.tax_image !== 'string' && {
              tax_image: data.tax_verification?.tax_image?.[0],
            }),
          };

    // Remove undefined values from payloads
    const cleanPayload = (payload) => Object.fromEntries(Object.entries(payload).filter(([_, v]) => v !== undefined));

    const cleanedGuestCommercialPayload = cleanPayload(guestCommercialRegistrationPayload);
    const cleanedTaxVerificationPayload = cleanPayload(taxVerificationPayload);

    // Check for dirty fields before sending payloads
    const hasDirtyGuestFields = Object.keys(dirtyFields?.guest_commercial_registration || {}).length > 0;
    const hasDirtyTaxFields = Object.keys(dirtyFields?.tax_verification || {}).length > 0;
    // Check if data exists before edit
    const isGuestCommercialNew = accountDetails?.guest_commercial_registration?.account_type || 'Individual';
    const newAccountType = data.guest_commercial_registration.account_type;

    const hasTaxChanges =
      dirtyFields?.tax_verification &&
      (dirtyFields?.tax_verification.tax_number ||
        dirtyFields?.tax_verification.tax_image ||
        dirtyFields?.tax_verification.registered_tax);

    let message = '';
    let withTitle = false;

    if (hasDirtyGuestFields) {
      if (isGuestCommercialNew === 'Individual' && newAccountType === 'Establishment') {
        message = hasTaxChanges
          ? t('successMessages.taxAndEstablishmentSuccess')
          : t('successMessages.establishmentDataSuccess');
        withTitle = true;
      } else if (isGuestCommercialNew === 'Establishment' && newAccountType === 'Individual') {
        message = t('successMessages.accountDataSuccess');
        withTitle = false;
      } else if (isGuestCommercialNew === 'Establishment' && newAccountType === 'Establishment') {
        message = t('successMessages.establishmentUpdateSuccess');
        withTitle = true;
      }
    }

    if (hasDirtyTaxFields && !hasDirtyGuestFields) {
      message = t('successMessages.accountDataSuccess');
      withTitle = false;
    }
    // Call appropriate mutations if fields are dirty
    const mutationPromises: Promise<any>[] = [];

    if (hasDirtyGuestFields && Object.keys(cleanedGuestCommercialPayload).length > 0) {
      mutationPromises.push(accountEditMutateAsync(cleanedGuestCommercialPayload as any));
    }

    if (hasDirtyTaxFields && Object.keys(cleanedTaxVerificationPayload).length > 0) {
      mutationPromises.push(editTaxMutateAsync(cleanedTaxVerificationPayload as any));
    }

    if (mutationPromises.length > 0) {
      try {
        await Promise.all(mutationPromises);
        setSuccessMessage({ withTitle, message });
        setIsConfirmDialogOpen(true);
        queryClient.refetchQueries({ queryKey: keys.accountDetails });
      } catch (error) {
        const Mess = error.response?.data?.message;
        toast.error(t(Mess));
      }
    }
  };
  const handleClose = () => {
    setIsConfirmDialogOpen(false);
  };

  const accountDetails = data?.data?.data || {};

  useEffect(() => {
    if (accountDetails?.guest_commercial_registration) {
      const {
        account_type,
        commercial_name,
        tax_number,
        tax_address,
        tax_image,
        commercial_image,
        commercial_registration_number,
        country_issue_id,
        expiry_date,
      } = accountDetails?.guest_commercial_registration;

      const isHaveTaxData = tax_number || tax_address;
      const country_issue = listOfCountries?.data?.data?.find(
        (country) => Number(country.id) === Number(country_issue_id)
      );
      reset({
        guest_commercial_registration: {
          account_type,
          commercial_name,
          commercial_image,
          commercial_registration_number,
          country_issue: country_issue?.short_name || country_issue?.id,
          expiry_date: expiry_date,
        },
        tax_verification: {
          tax_number: tax_number || '',
          tax_address: tax_address || '',
          tax_image,
          registered_tax: isHaveTaxData ? '1' : '0',
        },
      });
    }
  }, [accountDetails?.guest_commercial_registration, reset, listOfCountries]);

  if (accountPending) return <SkeletonLoader />;

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSaveDataChanges)}>
        <Card>
          <p className="text-secondary-300 px-6 text-2xl font-bold">{t('account_type')}</p>

          <div className="border-gray/20 border-y p-6">
            <AccountTypeSection accountDetails={accountDetails} />
            <TaxSection />
          </div>

          <div className="px-6">
            <Button
              type="submit"
              disabled={!isDirty || editAccountPending || editTaxPending}
              className="h-12 w-full shadow-none md:max-w-max"
              loading={editAccountPending || editTaxPending}>
              {t('save_data')}
            </Button>
          </div>
        </Card>
        {/* Confirmation Dialog */}
        <Dialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
          <DialogContent className="confirm-edit-dialog p-5">
            {successMessage.withTitle && (
              <DialogHeader className="border-b-0 pb-0">
                <DialogTitle>{t('dialog.title')}</DialogTitle>
              </DialogHeader>
            )}

            <div className="px-4 pb-4 text-center">
              <p className="text-secondary mb-5 text-lg font-bold">{successMessage.message}</p>
              <div className="flex justify-center">
                <Button className="bg-primary hover:bg-primary-400 h-12 w-full" onClick={handleClose}>
                  {t('dialog.Return_to_Account')}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </form>
    </FormProvider>
  );
};

export default AccountType;
